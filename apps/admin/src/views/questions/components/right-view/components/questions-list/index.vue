<script lang="ts" setup>
import { computed } from 'vue'
import type { ProcessedQuestionData } from '@sa/utils'

defineOptions({
  name: 'QuestionsList',
})

const questionItem = defineModel<ProcessedQuestionData>('questionItem', {
  default: () => ({}),
})

// 根据组件名称动态渲染对应的题型组件
const componentName = computed(() => {
  const name = questionItem.value.componentsName
  // 映射组件名称到实际的组件标签
  const componentMap: Record<string, string> = {
    SingleChoice: 'QSingleChoice', // 单选题
    MultipleChoice: 'QMultipleChoice', // 待实现
    TrueFalse: 'QTrueFalse', // 待实现
    FillBlank: 'QFillBlank', // 待实现
    ShortAnswer: 'QShortAnswer', // 待实现
    Essay: 'QEssay', // 待实现
  }
  return componentMap[name] || 'QSingleChoice'
})

// 判断是否为选择题类型（需要options）
const isChoiceType = computed(() => {
  return ['SingleChoice', 'MultipleChoice', 'TrueFalse'].includes(questionItem.value.componentsName)
})

// 判断当前组件是否已实现
const isComponentImplemented = computed(() => {
  return questionItem.value.componentsName === 'SingleChoice'
})
</script>

<template>
  <div>
    {{ componentName }}
    <!-- 动态渲染题型组件 -->
    <component
      :is="componentName"
      v-model="questionItem.correctAnswer"
      :options="isChoiceType ? questionItem.options : undefined"
      :title="questionItem.title"
      :question-data="questionItem"
    />

    <!-- 如果组件不存在，显示提示信息 -->
    <div v-if="!isComponentImplemented" class="py-4 text-center text-gray-500">
      <p>题型组件 "{{ questionItem.componentsName }}" 暂未实现</p>
      <p class="mt-2 text-sm">
        当前使用单选题组件作为默认显示
      </p>
    </div>
  </div>
</template>
