<script setup lang="ts">
import QSingleChoice from './q-single-choice'

defineOptions({
  name: 'QuestionItemContainer',
})
const props = defineProps<Props>()
interface Props {
  itemInfo: Question.ProcessedQuestionData
}
interface QuestionModule {
  label: string
  component: Component
}

const moduleMap: Record<Question.QuestionModule, QuestionModule> = {
  'single-choice': { label: '单选题', component: QSingleChoice },
  // MultipleChoice: { label: '多选题', component: QMultipleChoice },
  // TrueFalse: { label: '判断题', component: QTrueFalse },
  // FillBlank: { label: '填空题', component: QFillBlank },
}
const activeModule = computed(() => moduleMap[props.module || 'single-choice'])
</script>

<template>
  <div>1</div>
</template>
